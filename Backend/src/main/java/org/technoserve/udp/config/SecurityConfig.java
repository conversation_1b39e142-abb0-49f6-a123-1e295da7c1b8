package org.technoserve.udp.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.technoserve.udp.security.CustomAccessDeniedHandler;
import org.technoserve.udp.security.CustomAuthenticationEntryPoint;
import org.technoserve.udp.security.CustomEndpointAuthorization;
import org.technoserve.udp.security.CustomJwtAuthenticationConverter;
import org.technoserve.udp.service.CustomOidcUserService;

import java.io.IOException;


@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final CustomOidcUserService customOidcUserService;
    private final CustomEndpointAuthorization customEndpointAuth;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUrl;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authorize -> authorize
                        //  Allow Admin without checking logic
                        .requestMatchers("/role-management/**", "/user/**").hasAnyAuthority("ADMIN")
                        .requestMatchers("/master-screens").authenticated()
                        .requestMatchers("/", "/auth/**").authenticated()
                        .requestMatchers("/error").permitAll()
                        .anyRequest().access((authenticationSupplier, context) -> {
                            Authentication authentication = authenticationSupplier.get();
                            HttpServletRequest request = context.getRequest();
                            boolean granted = customEndpointAuth.hasAccess(request, authentication);
                            return new AuthorizationDecision(granted);
                        })
                ).exceptionHandling(exception -> exception
                        //  .authenticationEntryPoint(customAuthenticationEntryPoint()) // Handles 401
                        .accessDeniedHandler(customAccessDeniedHandler()) // Handles 403
                )
                .logout(httpSecurityLogoutConfigurer -> httpSecurityLogoutConfigurer
                        .logoutRequestMatcher(new AntPathRequestMatcher("/logout"))
                        .logoutSuccessHandler(customLogoutSuccessHandler())
                        .deleteCookies("JSSESSIONID")
                        .clearAuthentication(true)
                        .invalidateHttpSession(true))
                .oauth2Login(oauth2 -> oauth2
                        .userInfoEndpoint(userInfo -> userInfo
                                .oidcUserService(customOidcUserService)))
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwt -> jwt
                                .decoder(jwtDecoder())
                                .jwtAuthenticationConverter(customJwtAuthenticationConverter()))
                );

        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return JwtDecoders.fromIssuerLocation(issuerUrl);
    }

    @Bean
    public CustomJwtAuthenticationConverter customJwtAuthenticationConverter() {
        return new CustomJwtAuthenticationConverter();
    }

    @Bean
    public AuthenticationEntryPoint customAuthenticationEntryPoint() {
        return new CustomAuthenticationEntryPoint();
    }

    @Bean
    public AccessDeniedHandler customAccessDeniedHandler() {
        return new CustomAccessDeniedHandler();
    }
    @Bean
    public LogoutSuccessHandler customLogoutSuccessHandler() {
        return new LogoutSuccessHandler() {
            @Override
            public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, IOException {
                // Clear the security context and redirectpppppp
                request.getSession().invalidate();
                SecurityContextHolder.clearContext();
                String requestURL = request.getRequestURL().toString();
                String url= requestURL.substring(0,requestURL.indexOf("/udp/")) ;
                response.sendRedirect(url+"/logout-success");
            }
        };
    }
}
