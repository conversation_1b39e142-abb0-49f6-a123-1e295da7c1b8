package org.technoserve.udp.service;


import com.google.cloud.storage.Blob;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.entity.dataflow.*;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.*;
import org.technoserve.udp.service.processor.*;
import org.technoserve.udp.util.UdpCommonUtil;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DataFlowService {

  private static final Logger logger = LoggerFactory.getLogger(DataFlowService.class);

  private final GcpCloudServices gcpCloudServices;

  private final ProgramRepository programRepository;

  private final PartnerRepository partnerRepository;

  private final ExcelFileMetaDataRepository excelFileMetaDataRepository;

  private final ColumnMappingTemplateRepository columnMappingTemplateRepository;

  private final FarmerRepository farmerRepository;

  private final CentreRepository centreRepository;

  private final CentreTypeRepository centreTypeRepository;

  private final StaffRepository staffRepository;

  private final DesignationRepository designationRepository;

  private final UserRepository userRepository;

  private final EntityTypeRepository entityTypeRepository;

  private final MilkQualityRepository milkQualityRepository;

  private final DairyFieldDataRepository dairyFieldDataRepository;

  private final CottonFarmingDataRepository cottonFarmingDataRepository;

  private final ExcelFileMetaDataHistoryRepository excelFileMetaDataHistoryRepository;

  // Configuration for parallel processing
  private static final int THREAD_POOL_SIZE = 10; // Number of threads to use for parallel processing
  private static final int BATCH_SIZE = 100; // Number of records to process in a batch
  private static final double VALIDATION_THRESHOLD = 1.0; // Minimum percentage of valid rows required for processing (95%)
  private static final int EXECUTOR_TIMEOUT_SECONDS = 60; // Timeout for executor service shutdown
  private static final String VALIDATION_FAILED_MESSAGE = "Excel file validation failed. Only %d of %d rows were valid (%.1f%%). Minimum required: %.1f%%.";
  private static final String VALIDATION_PASSED_MESSAGE = "Excel file validated and processed successfully. %d of %d rows were valid (%.1f%%). %d rows were processed.";
  private static final String VALIDATED_SUFFIX = "_validated";
  private static final String VALIDATION_COLUMN_HEADER = "Validation Result";
  private static final String VALIDATION_VALID_VALUE = "Valid";


  @Transactional
  public Map<String, Object> uploadExcelAndSaveMeta(MultipartFile file, String fileType, Long programId, Long partnerId, String entityType) {

    Program program = programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found"));
    Partner partner = partnerRepository.findById(partnerId)
        .orElseThrow(() -> new ResourceNotFoundException("Partner not found"));

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmssSSS");

    String path = gcpCloudServices.uploadFile(file, "%d/%d/%s/%s".formatted(programId, partnerId, LocalDate.now(), LocalDateTime.now().format(formatter) + "_" + file.getOriginalFilename()));

    List<String> headers = new ArrayList<>();

    try (InputStream inputStream = new ByteArrayInputStream(file.getBytes())) {
      Workbook workbook = new XSSFWorkbook(inputStream);
      Sheet sheet = workbook.getSheetAt(0);
      Row headerRow = sheet.getRow(0);
      for (Cell cell : headerRow) {
        headers.add(cell.getStringCellValue());
      }

    } catch (IOException e) {
      throw new BadRequestException("Failed to read Excel file content: " + e.getMessage());
    }

    List<ColumnMappingTemplate> mappings = columnMappingTemplateRepository.findByProgramIdAndPartnerIdAndFileTypeAndEntityType(programId, partnerId, FileType.valueOf(fileType), entityType);
    boolean validTemplate = checkValidMappingExists(headers, mappings);

    ExcelFileMetaData savedEntity = excelFileMetaDataRepository.save(ExcelFileMetaData.builder()
        .fileName(file.getOriginalFilename())
        .path(path)
        .program(program)
        .partner(partner)
        .headerJson(headers)
        .fileType(FileType.valueOf(fileType))
        .entityType(entityType)
        .uploadStatus(UploadStatus.UPLOADED)
        .build());

    Map<String, Object> responseData = new HashMap<>();
    responseData.put("message", "File upload success");
    responseData.put("id", savedEntity.getExcelFileMetaDataId());
    responseData.put("isTemplateValid", validTemplate);

    return responseData;

  }

  /**
   * Get Excel headers and existing column mappings for a specific Excel file
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return DataflowColumnMappingResponse with mapped and unmapped columns
   */
  public DataflowColumnMappingResponse getExcelHeaders(Long excelFileMetaDataId) {
    ExcelFileMetaData excelFileMetaData = excelFileMetaDataRepository.findById(excelFileMetaDataId)
        .orElseThrow(() -> new ResourceNotFoundException("Excel File not found"));

    List<String> headers = excelFileMetaData.getHeaderJson();


    List<ColumnMappingTemplate> existingMappings = columnMappingTemplateRepository.findByProgramIdAndPartnerIdAndFileTypeAndEntityType(
        excelFileMetaData.getProgram().getProgramId(),
        excelFileMetaData.getPartner().getPartnerId(),
        excelFileMetaData.getFileType(),
        excelFileMetaData.getEntityType());

    ExcelFileMetaDataResponse excelFileMetaDataResponse = convertToExcelFileMetaDataResponse(excelFileMetaData);


    List<FieldInfoResponse> fieldInfoResponses = null;
    try {
      fieldInfoResponses = UdpCommonUtil.getEntityFields(excelFileMetaData.getEntityType());
    } catch (ClassNotFoundException e) {
      throw new BadRequestException("Entity Type not matching with the Entity in system");
    }

    // Build a map for quick lookup of entity field -> excel column
    Map<String, String> fieldToExcelColumnMap = existingMappings.stream()
        .collect(Collectors.toMap(ColumnMappingTemplate::getEntityField, ColumnMappingTemplate::getExcelColumn));

    List<FieldMappingDto> mappings = fieldInfoResponses.stream()
        .map(field -> {
          String excelColumn = fieldToExcelColumnMap.getOrDefault(field.getField(), null);
          return new FieldMappingDto(field.getField(), field.getName(), excelColumn);
        })
        .toList();

    return new DataflowColumnMappingResponse(excelFileMetaDataResponse,mappings, headers);
  }


  /**
   * Validate and process an Excel file in parallel in a single operation
   * First validates the file and then processes it if validation passes
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return ValidateAndProcessResponse with validation and processing results
   */
  @Transactional
  public ValidateAndProcessResponse validateAndProcessExcelFileInParallel(Long excelFileMetaDataId) {
    // Get the Excel file metadata
    ExcelFileMetaData excelFileMetaData = excelFileMetaDataRepository.findById(excelFileMetaDataId)
        .orElseThrow(() -> new ResourceNotFoundException("Excel file metadata not found"));

    if (excelFileMetaData.getUploadStatus().equals(UploadStatus.PROCESSED)) {
      throw new BadRequestException("File already processed. Try reuploading the file.");
    }

    // Get the file from GCP
    Blob blob = gcpCloudServices.getFileBlob(excelFileMetaData.getPath());
    if (blob == null) {
      throw new ResourceNotFoundException("Excel file not found in storage");
    }

    // Get the column mappings
    List<ColumnMappingTemplate> columnMappings = columnMappingTemplateRepository
        .findByProgramIdAndPartnerIdAndFileTypeAndEntityType(
            excelFileMetaData.getProgram().getProgramId(),
            excelFileMetaData.getPartner().getPartnerId(),
            excelFileMetaData.getFileType(),
            excelFileMetaData.getEntityType());

    if (columnMappings.isEmpty()) {
      throw new BadRequestException("No column mappings found for this file");
    }

    boolean validTemplate = checkValidMappingExists(excelFileMetaData.getHeaderJson(), columnMappings);
    if (!validTemplate) {
      throw new BadRequestException("Column mappings do not match the Excel file headers");
    }

    // Create a map of Excel column names to entity field names for backward compatibility
    Map<String, String> mappingsMap = columnMappings.stream()
        .collect(Collectors.toMap(ColumnMappingTemplate::getEntityField, ColumnMappingTemplate::getExcelColumn));

    try (InputStream inputStream = new ByteArrayInputStream(blob.getContent());
         Workbook workbook = new XSSFWorkbook(inputStream)) {
      FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
      Sheet sheet = workbook.getSheetAt(0);
      Row headerRow = sheet.getRow(0);

      // Create a map of column indices to column names
      Map<Integer, String> columnIndices = new HashMap<>();
      for (int i = 0; i < headerRow.getLastCellNum(); i++) {
        Cell cell = headerRow.getCell(i);
        if (cell != null) {
          columnIndices.put(i, cell.getStringCellValue());
        }
      }

      // Get the entity type, program, and partner
      String entityType = excelFileMetaData.getEntityType();
      Program program = excelFileMetaData.getProgram();
      Partner partner = excelFileMetaData.getPartner();


      // Delegate to the appropriate processor based on entity type
      DataProcessor processor = getDataProcessorForFileTypeAndEntityType(entityType, evaluator);
      if (processor == null) {
        throw new BadRequestException("Unsupported entity type: " + entityType);
      }

      // Step 1: Validate the data
      ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
      List<ValidationResult> validationResults;
      int processedRows = 0;
      String message;
      String validatedFilePath = "";

      try {
        // Validate data in parallel
        validationResults = processor.validateDataInParallel(sheet, columnIndices, mappingsMap,
            program, partner, excelFileMetaData, executor);

        // Calculate validation statistics
        int totalRows = validationResults.size();
        int validRows = (int) validationResults.stream().filter(ValidationResult::isValid).count();
        int invalidRows = totalRows - validRows;
        double validPercentage = totalRows > 0 ? (double) validRows / totalRows : 0;
        boolean validationPassed = validPercentage >= VALIDATION_THRESHOLD;

        // Add validation results to the Excel file
        Workbook validatedWorkbook = addValidationResultsToExcel(workbook, validationResults);


        // Step 2: Process the data if validation passes
        if (validationPassed) {
          // Process the data in parallel
          try {
            processedRows = processor.processDataInParallel(sheet, columnIndices, mappingsMap,
                program, partner, excelFileMetaData, BATCH_SIZE, executor);

            // Update the status of the Excel file metadata
            excelFileMetaData.setUploadStatus(UploadStatus.PROCESSED);

            // Set processed information
            excelFileMetaData.setProcessedOn(LocalDateTime.now());

            // Get current user if available
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            // Check if the user is authenticated via OIDC
            if (authentication != null && authentication.isAuthenticated() && authentication.getPrincipal() instanceof OidcUser oidcUser) {
              String email = oidcUser.getEmail(); // Extract email from OIDC token
              Optional<UserEntity> user = userRepository.findById(email);
              excelFileMetaData.setProcessedBy(user.map(UserEntity::getEmail).orElse(null));
            }
            excelFileMetaData.setValidationResultPath("");
            excelFileMetaDataRepository.save(excelFileMetaData);

            message = String.format(VALIDATION_PASSED_MESSAGE,
                validRows, totalRows, validPercentage * 100, processedRows);
          } catch (Exception e) {
            excelFileMetaData.setUploadStatus(UploadStatus.FAILED);
            excelFileMetaDataRepository.save(excelFileMetaData);
            throw e;
          }
        } else {
          // Update the status of the Excel file metadata to VALIDATION_FAILED
          excelFileMetaData.setUploadStatus(UploadStatus.FAILED);

          // Save the validated Excel file to GCP
          validatedFilePath = saveValidatedExcelFile(validatedWorkbook, excelFileMetaData);
          excelFileMetaData.setValidationResultPath(validatedFilePath);
          excelFileMetaDataRepository.save(excelFileMetaData);
          message = String.format(VALIDATION_FAILED_MESSAGE,
              validRows, totalRows, validPercentage * 100, VALIDATION_THRESHOLD * 100);

        }

        // Return the combined results
        return ValidateAndProcessResponse.builder()
            .validationPassed(validationPassed)
            .totalRows(totalRows)
            .validRows(validRows)
            .invalidRows(invalidRows)
            .processedRows(processedRows)
            .validatedFilePath(validatedFilePath)
            .message(message)
            .build();
      } finally {
        // Shutdown the executor
        shutdownExecutorService(executor);
      }
    } catch (IOException e) {
      // Update the status of the Excel file metadata to FAILED
      excelFileMetaData.setUploadStatus(UploadStatus.FAILED);
      excelFileMetaDataRepository.save(excelFileMetaData);

      throw new BadRequestException("Error processing Excel file: " + e.getMessage());
    } catch (Exception e) {
      // Update the status of the Excel file metadata to FAILED
      excelFileMetaData.setUploadStatus(UploadStatus.FAILED);
      excelFileMetaDataRepository.save(excelFileMetaData);

      throw new BadRequestException("Unexpected error during Excel file processing: " + e.getMessage());
    }
  }

  /**
   * Get the appropriate data processor for the given entity type
   *
   * @param entityType The entity type
   * @return The data processor for the entity type, or null if not supported
   */
  private DataProcessor getDataProcessorForFileTypeAndEntityType(String entityType, FormulaEvaluator evaluator) {

    switch (entityType) {
      case "Farmer":
        return new FarmerDataProcessor(farmerRepository, centreRepository, evaluator);
      case "Centre":
        return new CentreDataProcessor(centreRepository, centreTypeRepository, evaluator);
      case "Staff":
        return new StaffDataProcessor(staffRepository, centreRepository, designationRepository, evaluator);
      case "MilkQuality":
        return new MilkQualityDataProcessor(milkQualityRepository, centreRepository, evaluator);
      case "DairyFieldData":
        return new DairyFieldDataProcessor(dairyFieldDataRepository, evaluator);
      case "CottonFarmingData":
        return new CottonFarmingDataProcessor(cottonFarmingDataRepository, farmerRepository, evaluator);

      default:
        return null;
    }

  }

  /**
   * Add validation results to an Excel file
   *
   * @param workbook          The Excel workbook
   * @param validationResults The validation results
   * @return The Excel workbook with validation results
   */
  private Workbook addValidationResultsToExcel(Workbook workbook, List<ValidationResult> validationResults) {
    Sheet sheet = workbook.getSheetAt(0);
    if (sheet == null) {
      return workbook;
    }

    Row headerRow = sheet.getRow(0);
    if (headerRow == null) {
      return workbook;
    }

    // Add a new column header for validation results
    int lastCellNum = headerRow.getLastCellNum();
    Cell validationHeaderCell = headerRow.createCell(lastCellNum);
    validationHeaderCell.setCellValue(VALIDATION_COLUMN_HEADER);

    // Reuse styles for error and valid cells
    CellStyle errorStyle = workbook.createCellStyle();
    errorStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
    errorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

    CellStyle validStyle = workbook.createCellStyle();
    validStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
    validStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

    // Add validation results to each row
    for (ValidationResult result : validationResults) {
      int rowIndex = result.getRowIndex();
      Row row = sheet.getRow(rowIndex);

      if (row != null) {
        Cell validationCell = row.getCell(lastCellNum);
        if (validationCell == null) {
          validationCell = row.createCell(lastCellNum);
        }

        if (!result.isValid()) {
          validationCell.setCellValue(result.getErrorMessage());
          validationCell.setCellStyle(errorStyle);
        } else {
          validationCell.setCellValue(VALIDATION_VALID_VALUE);
          validationCell.setCellStyle(validStyle);
        }
      }
    }

    // Auto-size the validation column
    sheet.autoSizeColumn(lastCellNum);

    return workbook;
  }

  /**
   * Save a validated Excel file to GCP
   *
   * @param workbook          The Excel workbook
   * @param excelFileMetaData The Excel file metadata
   * @return The path to the saved file
   * @throws IOException If an I/O error occurs
   */
  private String saveValidatedExcelFile(Workbook workbook, ExcelFileMetaData excelFileMetaData) throws IOException {
    // Create a new file name with validated suffix
    String originalFileName = excelFileMetaData.getFileName();
    if (originalFileName == null || originalFileName.isEmpty()) {
      throw new IllegalArgumentException("Original file name is missing");
    }

    int lastDotIndex = originalFileName.lastIndexOf('.');
    if (lastDotIndex == -1) {
      throw new IllegalArgumentException("Invalid file name format: " + originalFileName);
    }

    String fileNameWithoutExt = originalFileName.substring(0, lastDotIndex);
    String extension = originalFileName.substring(lastDotIndex);
    String validatedFileName = fileNameWithoutExt + VALIDATED_SUFFIX + extension;

    // Create the path for the validated file
    String validatedFilePath = excelFileMetaData.getPath().replace(originalFileName, validatedFileName);

    // Write the workbook to a byte array
    byte[] bytes;
    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      workbook.write(outputStream);
      bytes = outputStream.toByteArray();
    }

    // Upload the file to GCP
    ByteArrayResource resource = new ByteArrayResource(bytes);
    gcpCloudServices.uploadFile(resource, validatedFilePath);

    return validatedFilePath;
  }

  /**
   * Get Excel file metadata by program ID, partner ID, and entity type with pagination
   *
   * @param programId  The program ID (optional)
   * @param partnerId  The partner ID (optional)
   * @param entityType The entity type (optional)
   * @param pageable   The pageable object for pagination
   * @return Page of Excel file metadata responses
   */
  public Page<ExcelFileMetaDataResponse> getExcelFileMetaData(Long programId, Long partnerId,
                                                              String fileType, String entityType, Pageable pageable) {
    // Get the program and partner if IDs are provided
    Program program = programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));

    Partner partner = partnerRepository.findById(partnerId)
        .orElseThrow(() -> new ResourceNotFoundException("Partner not found with ID: " + partnerId));


    // Convert entity type string to enum if provided
    FileType fileTypeEnum = null;
    if (fileType != null && !fileType.isEmpty()) {
      try {
        fileTypeEnum = FileType.valueOf(fileType);
      } catch (IllegalArgumentException e) {
        throw new BadRequestException("Invalid file type: " + fileType);
      }
    }

    Page<ExcelFileMetaData> metadataList = excelFileMetaDataRepository
        .findByProgramAndPartnerAndFileTypeAndEntityType(program, partner, fileTypeEnum, entityType, pageable);

    // Convert to response DTOs
    return metadataList.map(this::convertToExcelFileMetaDataResponse);
  }

  /**
   * Convert ExcelFileMetaData entity to ExcelFileMetaDataResponse DTO
   *
   * @param metadata The Excel file metadata entity
   * @return The Excel file metadata response DTO
   */
  private ExcelFileMetaDataResponse convertToExcelFileMetaDataResponse(ExcelFileMetaData metadata) {
    return ExcelFileMetaDataResponse.builder()
        .id(metadata.getExcelFileMetaDataId())
        .fileName(metadata.getFileName())
        .path(metadata.getPath())
        .programId(metadata.getProgram() != null ? metadata.getProgram().getProgramId() : null)
        .programName(metadata.getProgram() != null ? metadata.getProgram().getName() : null)
        .partnerId(metadata.getPartner() != null ? metadata.getPartner().getPartnerId() : null)
        .partnerName(metadata.getPartner() != null ? metadata.getPartner().getName() : null)
        .entityType(metadata.getEntityType())
        .validationResultPath(metadata.getValidationResultPath())
        .uploadStatus(metadata.getUploadStatus())
        .uploadedOn(metadata.getUploadedOn())
        .uploadedBy(metadata.getUploadedBy())
        .processedOn(metadata.getProcessedOn())
        .processedBy(metadata.getProcessedBy())
        .build();
  }

  /**
   * Safely shut down an executor service
   *
   * @param executor The executor service to shut down
   */
  private void shutdownExecutorService(ExecutorService executor) {
    if (executor != null && !executor.isShutdown()) {
      executor.shutdown();
      try {
        if (!executor.awaitTermination(EXECUTOR_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
          executor.shutdownNow();
          if (!executor.awaitTermination(EXECUTOR_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
            logger.error("Thread pool did not terminate");
          }
        }
      } catch (InterruptedException ie) {
        executor.shutdownNow();
        Thread.currentThread().interrupt();
        logger.error("Thread pool shutdown interrupted", ie);
      }
    }
  }

  /**
   * Reupload an Excel file for a specific Excel file metadata ID
   * This will delete all existing data related to the Excel file and reset the status to UPLOADED
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @param file                The new Excel file to upload
   * @return ApiResponse with success message and the Excel file metadata ID
   */
  @Transactional
  public Map<String, Object> reuploadExcelFile(Long excelFileMetaDataId, MultipartFile file) {
    // Get the Excel file metadata
    ExcelFileMetaData excelFileMetaData = excelFileMetaDataRepository.findById(excelFileMetaDataId)
        .orElseThrow(() -> new ResourceNotFoundException("Excel file metadata not found"));

    // Get the current user
    String currentUser = getCurrentUser();

    // Create a history record before making changes
    ExcelFileMetaDataHistory history = ExcelFileMetaDataHistory.fromExcelFileMetaData(excelFileMetaData, currentUser);
    excelFileMetaDataHistoryRepository.save(history);

    // Delete all related data based on entity type
    deleteRelatedData(excelFileMetaData);

    // Upload the new file
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmssSSS");
    Program program = excelFileMetaData.getProgram();
    Partner partner = excelFileMetaData.getPartner();
    String path = gcpCloudServices.uploadFile(file, "%d/%d/%s/%s".formatted(
        program.getProgramId(),
        partner.getPartnerId(),
        LocalDate.now(),
        LocalDateTime.now().format(formatter) + "_" + file.getOriginalFilename()
    ));

    // Extract headers from the new file
    List<String> headers = new ArrayList<>();
    try (InputStream inputStream = new ByteArrayInputStream(file.getBytes())) {
      Workbook workbook = new XSSFWorkbook(inputStream);
      Sheet sheet = workbook.getSheetAt(0);
      Row headerRow = sheet.getRow(0);
      for (Cell cell : headerRow) {
        headers.add(cell.getStringCellValue());
      }
    } catch (IOException e) {
      throw new BadRequestException("Failed to read Excel file content: " + e.getMessage());
    }

    List<ColumnMappingTemplate> mappings = columnMappingTemplateRepository.findByProgramIdAndPartnerIdAndFileTypeAndEntityType(program.getProgramId(), partner.getPartnerId(), excelFileMetaData.getFileType(), excelFileMetaData.getEntityType());
    boolean validTemplate = checkValidMappingExists(headers, mappings);


    // Update the Excel file metadata
    excelFileMetaData.setFileName(file.getOriginalFilename());
    excelFileMetaData.setPath(path);
    excelFileMetaData.setHeaderJson(headers);
    excelFileMetaData.setUploadedBy(currentUser);
    excelFileMetaData.setUploadedOn(LocalDateTime.now());
    excelFileMetaData.setUploadStatus(UploadStatus.UPLOADED);
    excelFileMetaData.setValidationResultPath(null);
    excelFileMetaData.setProcessedBy(null);
    excelFileMetaData.setProcessedOn(null);

    // Save the updated metadata
    excelFileMetaDataRepository.save(excelFileMetaData);

    // Create a response with template validity information
    Map<String, Object> responseData = new HashMap<>();
    responseData.put("message", "File reuploaded successfully");
    responseData.put("id", excelFileMetaData.getExcelFileMetaDataId());
    responseData.put("isTemplateValid", validTemplate);

    return responseData;
  }

  /**
   * Delete all data related to an Excel file metadata
   *
   * @param excelFileMetaData The Excel file metadata
   */
  private void deleteRelatedData(ExcelFileMetaData excelFileMetaData) {
    String entityType = excelFileMetaData.getEntityType();

    switch (entityType) {
      case "Farmer":
        // Delete all farmers related to this Excel file metadata
        farmerRepository.deleteAllByExcelFileMetaData_ExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
      case "Centre":
        // Delete all centre related to this Excel file metadata
        centreRepository.deleteAllByExcelFileMetaData_ExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
      case "Staff":
        // Delete all staff related to this Excel file metadata
        staffRepository.deleteAllByExcelFileMetaData_ExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
      case "MilkQuality":
        // Delete all milk quality data related to this Excel file metadata
        milkQualityRepository.deleteByExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
        case "DairyFieldData":
        // Delete all dairy field data related to this Excel file metadata
        dairyFieldDataRepository.deleteAllByExcelFileMetaData_ExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
        case "CottonFarmingData":
        // Delete all cotton farming data related to this Excel file metadata
        cottonFarmingDataRepository.deleteAllByExcelFileMetaData_ExcelFileMetaDataId(excelFileMetaData.getExcelFileMetaDataId());
        break;
      default:
        throw new BadRequestException("Unsupported entity type for reupload: " + entityType);
    }
  }

  /**
   * Get the current authenticated user's email
   *
   * @return The current user's email or "system" if not authenticated
   */
  private String getCurrentUser() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.isAuthenticated()) {
      if (authentication.getPrincipal() instanceof OidcUser oidcUser) {
        return oidcUser.getEmail();
      }
      return authentication.getName();
    }
    return "system";
  }

  /**
   * Get the history of an Excel file metadata
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return List of Excel file metadata history records
   */
  public List<ExcelFileMetaDataHistory> getExcelFileMetaDataHistory(Long excelFileMetaDataId) {
    // Check if the Excel file metadata exists
    if (!excelFileMetaDataRepository.existsById(excelFileMetaDataId)) {
      throw new ResourceNotFoundException("Excel file metadata not found with ID: " + excelFileMetaDataId);
    }

    // Get the history records
    return excelFileMetaDataHistoryRepository.findByExcelFileMetaDataIdOrderByArchivedOnDesc(excelFileMetaDataId);
  }

  /**
   * Soft delete an Excel file metadata and mark it as DELETED
   * This will not actually delete the record, but just update its status
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return ApiResponse with success message
   */
  @Transactional
  public ApiResponse softDeleteExcelFile(Long excelFileMetaDataId) {
    // Get the Excel file metadata
    ExcelFileMetaData excelFileMetaData = excelFileMetaDataRepository.findById(excelFileMetaDataId)
        .orElseThrow(() -> new ResourceNotFoundException("Excel file metadata not found"));

    // Check if it's already deleted
    if (excelFileMetaData.getUploadStatus() == UploadStatus.DELETED) {
      return new ApiResponse("File already marked as deleted", excelFileMetaDataId);
    }

    // Get the current user
    String currentUser = getCurrentUser();

    // Create a history record before making changes
    ExcelFileMetaDataHistory history = ExcelFileMetaDataHistory.fromExcelFileMetaData(excelFileMetaData, currentUser);
    excelFileMetaDataHistoryRepository.save(history);

    // Delete all related data based on entity type
    deleteRelatedData(excelFileMetaData);

    // Update the status to DELETED
    excelFileMetaData.setUploadStatus(UploadStatus.DELETED);
    excelFileMetaDataRepository.save(excelFileMetaData);

    return new ApiResponse("File and related data deleted successfully", excelFileMetaDataId);
  }

  /**
   * Save or update column mappings for a specific program, partner, and entity type
   * If mappings already exist for the combination, they will be deleted and new ones created
   *
   * @param programId  The program ID
   * @param partnerId  The partner ID
   * @param fileType   The file type
   * @param entityType The entity type
   * @param mappings   List of column mappings with excelColumn and entityField
   * @return ApiResponse with success message and count of mappings saved
   */
  @Transactional
  public ApiResponse saveOrUpdateColumnMappings(Long programId, Long partnerId, String fileType, String entityType, List<ColumnMappingDto> mappings) {
    // Validate inputs
    Program program = programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));

    Partner partner = partnerRepository.findById(partnerId)
        .orElseThrow(() -> new ResourceNotFoundException("Partner not found with ID: " + partnerId));

    FileType fileTypeEnum;
    try {
      fileTypeEnum = FileType.valueOf(fileType);
    } catch (IllegalArgumentException e) {
      throw new BadRequestException("Invalid entity type");
    }

    // Find existing mappings
    List<ColumnMappingTemplate> existingMappings = columnMappingTemplateRepository
        .findByProgramIdAndPartnerIdAndFileTypeAndEntityType(program.getProgramId(), partner.getPartnerId(), fileTypeEnum, entityType);

    // Delete all existing mappings for this program, partner, and entity type
    if (!existingMappings.isEmpty()) {
      logger.info("Deleting {} existing column mappings for programId={}, partnerId={}, entityType={}",
          existingMappings.size(), programId, partnerId, entityType);
      columnMappingTemplateRepository.deleteAll(existingMappings);
    }

    // Create new mappings
    List<ColumnMappingTemplate> newMappings = new ArrayList<>();

    // Process each mapping
    for (ColumnMappingDto mapping : mappings) {
      String excelColumn = mapping.getExcelColumn();
      String entityField = mapping.getEntityField();

      // Create new mapping
      ColumnMappingTemplate newMapping = ColumnMappingTemplate.builder()
          .programId(programId)
          .partnerId(partnerId)
          .fileType(fileTypeEnum)
          .entityType(entityType)
          .excelColumn(excelColumn)
          .entityField(entityField)
          .build();
      newMappings.add(newMapping);
    }

    // Save all new mappings
    List<ColumnMappingTemplate> savedMappings = columnMappingTemplateRepository.saveAll(newMappings);
    logger.info("Saved {} new column mappings for programId={}, partnerId={}, entityType={}",
        savedMappings.size(), programId, partnerId, entityType);

    return new ApiResponse("Column mappings saved successfully", savedMappings.size());
  }


  private boolean checkValidMappingExists(List<String> headers, List<ColumnMappingTemplate> mappings) {
    if (mappings.isEmpty()) {
      return false;
    }
    return mappings.stream().allMatch(mapping -> headers.contains(mapping.getExcelColumn()));
  }
}
