package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.CentreId;

import java.util.List;
import java.util.Optional;

@Repository
public interface CentreRepository extends JpaRepository<Centre, CentreId> {

    Optional<Centre> findByCentreIdAndProgramIdAndPartnerId(String centreId, Long programId, Long partnerId);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);

    /**
     * Find centres by program ID and partner ID
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @return List of centres
     */
    @Query("SELECT c FROM Centre c WHERE "
        + "(:programId IS NULL OR c.programId = :programId) AND "
        + "(:partnerId IS NULL OR c.partnerId = :partnerId)")
    List<Centre> findByProgramIdAndPartnerId(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId);

    /**
     * Find centres by program ID and partner ID with sorting
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param sort The sort object for sorting
     * @return List of centres
     */
    @Query("SELECT c FROM Centre c WHERE "
        + "(:programId IS NULL OR c.programId = :programId) AND "
        + "(:partnerId IS NULL OR c.partnerId = :partnerId)")
    List<Centre> findByProgramIdAndPartnerId(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        Sort sort);

    /**
     * Find centres by program ID and partner ID with pagination and sorting
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param pageable The pageable object for pagination and sorting
     * @return Page of centres
     */
    @Query("SELECT c FROM Centre c WHERE "
        + "(:programId IS NULL OR c.programId = :programId) AND "
        + "(:partnerId IS NULL OR c.partnerId = :partnerId)")
    Page<Centre> findByProgramIdAndPartnerId(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        Pageable pageable);
}
