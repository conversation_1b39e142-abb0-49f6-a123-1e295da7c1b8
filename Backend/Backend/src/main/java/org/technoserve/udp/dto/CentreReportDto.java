package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Centre Report
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CentreReportDto {
    private String centreId;
    private String centreName;
    private String centreType;
    private String routeNo;
    private String routeName;
    private String facilitatorName;
    private String facilitatorId;
    private String facilitatorCountryCode;
    private String facilitatorMobileNumber;
    private String state;
    private String district;
    private String taluk;
    private String village;
    private Double latitude;
    private Double longitude;
    private Double installedCapacity;
    private String icsType;
    private String partnerName;
    private String programName;
}
