package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.CentreReportDto;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.PartnerRepository;
import org.technoserve.udp.repository.ProgramRepository;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ExcelExportService {

    private final CentreRepository centreRepository;
    private final PartnerRepository partnerRepository;
    private final ProgramRepository programRepository;

    /**
     * Generate centre report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCentreReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all centre data without pagination for Excel export
        List<CentreReportDto> centreData = getAllCentreReportData(programId, partnerId, sortBy, sortDir);

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Centre Report");

            // Create header style
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            // Create data style
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Centre ID", "Centre Name", "Centre Type", "Route No", "Route Name",
                "Facilitator Name", "Facilitator ID", "Facilitator Country Code", "Facilitator Mobile Number",
                "State", "District", "Taluk", "Village", "Latitude", "Longitude",
                "Installed Capacity", "ICS Type", "Partner Name", "Program Name"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (CentreReportDto centre : centreData) {
                Row row = sheet.createRow(rowNum++);

                setCellValue(row, 0, centre.getCentreId(), dataStyle);
                setCellValue(row, 1, centre.getCentreName(), dataStyle);
                setCellValue(row, 2, centre.getCentreType(), dataStyle);
                setCellValue(row, 3, centre.getRouteNo(), dataStyle);
                setCellValue(row, 4, centre.getRouteName(), dataStyle);
                setCellValue(row, 5, centre.getFacilitatorName(), dataStyle);
                setCellValue(row, 6, centre.getFacilitatorId(), dataStyle);
                setCellValue(row, 7, centre.getFacilitatorCountryCode(), dataStyle);
                setCellValue(row, 8, centre.getFacilitatorMobileNumber(), dataStyle);
                setCellValue(row, 9, centre.getState(), dataStyle);
                setCellValue(row, 10, centre.getDistrict(), dataStyle);
                setCellValue(row, 11, centre.getTaluk(), dataStyle);
                setCellValue(row, 12, centre.getVillage(), dataStyle);
                setCellValue(row, 13, centre.getLatitude(), dataStyle);
                setCellValue(row, 14, centre.getLongitude(), dataStyle);
                setCellValue(row, 15, centre.getInstalledCapacity(), dataStyle);
                setCellValue(row, 16, centre.getIcsType(), dataStyle);
                setCellValue(row, 17, centre.getPartnerName(), dataStyle);
                setCellValue(row, 18, centre.getProgramName(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Get all centre report data without pagination for Excel export
     */
    private List<CentreReportDto> getAllCentreReportData(Long programId, Long partnerId, String sortBy, String sortDir) {
        // Create a Sort object for the query
        Sort sort = Sort.by(sortBy);
        sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

        // Get all centres with sorting directly from the database
        List<Centre> centres = centreRepository.findByProgramIdAndPartnerId(programId, partnerId, sort);

        // Convert centres to DTOs
        List<CentreReportDto> reportDtos = new ArrayList<>();

        for (Centre centre : centres) {
            // Get partner name
            String partnerName = "Unknown";
            Optional<Partner> partnerOpt = partnerRepository.findById(centre.getPartnerId());
            if (partnerOpt.isPresent()) {
                partnerName = partnerOpt.get().getName();
            }

            // Get program name
            String programName = "Unknown";
            Optional<Program> programOpt = programRepository.findById(centre.getProgramId());
            if (programOpt.isPresent()) {
                programName = programOpt.get().getName();
            }

            // Create the report DTO
            CentreReportDto reportDto = CentreReportDto.builder()
                .centreId(centre.getCentreId())
                .centreName(centre.getCentreName())
                .centreType(centre.getCentreType() != null ? centre.getCentreType().getType() : null)
                .routeNo(centre.getRouteNo())
                .routeName(centre.getRouteName())
                .facilitatorName(centre.getFacilitatorName())
                .facilitatorId(centre.getFacilitatorId())
                .facilitatorCountryCode(centre.getFacilitatorCountryCode())
                .facilitatorMobileNumber(centre.getFacilitatorMobileNumber())
                .state(centre.getState())
                .district(centre.getDistrict())
                .taluk(centre.getTaluk())
                .village(centre.getVillage())
                .latitude(centre.getLatitude())
                .longitude(centre.getLongitude())
                .installedCapacity(centre.getInstalledCapacity())
                .icsType(centre.getIcsType())
                .partnerName(partnerName)
                .programName(programName)
                .build();

            reportDtos.add(reportDto);
        }

        return reportDtos;
    }

    /**
     * Helper method to set cell value with proper type handling
     */
    private void setCellValue(Row row, int columnIndex, Object value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellStyle(style);

        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }
}
